// Base theme definitions
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    muted: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  syntax: {
    keyword: string;
    string: string;
    number: string;
    comment: string;
    function: string;
    variable: string;
    type: string;
    operator: string;
  };
  ui: {
    border: string;
    selection: string;
    cursor: string;
    highlight: string;
    disabled: string;
  };
}

export const defaultTheme: Theme = {
  name: 'default',
  colors: {
    primary: 'blue',
    secondary: 'cyan',
    accent: 'magenta',
    background: 'black',
    foreground: 'white',
    muted: 'gray',
    success: 'green',
    warning: 'yellow',
    error: 'red',
    info: 'blue'
  },
  syntax: {
    keyword: 'blue',
    string: 'green',
    number: 'yellow',
    comment: 'gray',
    function: 'cyan',
    variable: 'white',
    type: 'magenta',
    operator: 'red'
  },
  ui: {
    border: 'gray',
    selection: 'blue',
    cursor: 'white',
    highlight: 'yellow',
    disabled: 'gray'
  }
};

export const draculaTheme: Theme = {
  name: 'dracula',
  colors: {
    primary: '#bd93f9',
    secondary: '#8be9fd',
    accent: '#ff79c6',
    background: '#282a36',
    foreground: '#f8f8f2',
    muted: '#6272a4',
    success: '#50fa7b',
    warning: '#f1fa8c',
    error: '#ff5555',
    info: '#8be9fd'
  },
  syntax: {
    keyword: '#ff79c6',
    string: '#f1fa8c',
    number: '#bd93f9',
    comment: '#6272a4',
    function: '#50fa7b',
    variable: '#f8f8f2',
    type: '#8be9fd',
    operator: '#ff79c6'
  },
  ui: {
    border: '#6272a4',
    selection: '#44475a',
    cursor: '#f8f8f2',
    highlight: '#f1fa8c',
    disabled: '#6272a4'
  }
};

export const githubDarkTheme: Theme = {
  name: 'github-dark',
  colors: {
    primary: '#58a6ff',
    secondary: '#79c0ff',
    accent: '#d2a8ff',
    background: '#0d1117',
    foreground: '#c9d1d9',
    muted: '#8b949e',
    success: '#3fb950',
    warning: '#d29922',
    error: '#f85149',
    info: '#58a6ff'
  },
  syntax: {
    keyword: '#ff7b72',
    string: '#a5d6ff',
    number: '#79c0ff',
    comment: '#8b949e',
    function: '#d2a8ff',
    variable: '#c9d1d9',
    type: '#ffa657',
    operator: '#ff7b72'
  },
  ui: {
    border: '#30363d',
    selection: '#264f78',
    cursor: '#c9d1d9',
    highlight: '#d29922',
    disabled: '#484f58'
  }
};

export const availableThemes: Record<string, Theme> = {
  default: defaultTheme,
  dracula: draculaTheme,
  'github-dark': githubDarkTheme
};

export function getTheme(themeName: string): Theme {
  return availableThemes[themeName] || defaultTheme;
}

export function getAvailableThemes(): string[] {
  return Object.keys(availableThemes);
}
